<?php
/**
 * Admin functionality for Modern SEO Tools
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Modern_SEO_Tools_Admin {

    /**
     * Database handler
     */
    private $database;

    /**
     * Tool manager
     */
    private $tool_manager;

    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Modern_SEO_Tools_Database();
        $this->tool_manager = new Modern_SEO_Tools_Tool_Manager();
    }

    /**
     * Initialize admin functionality
     */
    public function init() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_mst_save_tool', array($this, 'ajax_save_tool'));
        add_action('wp_ajax_mst_delete_tool', array($this, 'ajax_delete_tool'));
        add_action('wp_ajax_mst_toggle_tool_status', array($this, 'ajax_toggle_tool_status'));
        add_action('admin_init', array($this, 'handle_form_submissions'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Modern SEO Tools', 'modern-seo-tools'),
            __('SEO Tools', 'modern-seo-tools'),
            'manage_options',
            'modern-seo-tools',
            array($this, 'admin_page_tools_list'),
            'dashicons-code-standards',
            30
        );

        add_submenu_page(
            'modern-seo-tools',
            __('All Tools', 'modern-seo-tools'),
            __('All Tools', 'modern-seo-tools'),
            'manage_options',
            'modern-seo-tools',
            array($this, 'admin_page_tools_list')
        );

        add_submenu_page(
            'modern-seo-tools',
            __('Add New Tool', 'modern-seo-tools'),
            __('Add New', 'modern-seo-tools'),
            'manage_options',
            'modern-seo-tools-add',
            array($this, 'admin_page_add_tool')
        );

        add_submenu_page(
            'modern-seo-tools',
            __('Settings', 'modern-seo-tools'),
            __('Settings', 'modern-seo-tools'),
            'manage_options',
            'modern-seo-tools-settings',
            array($this, 'admin_page_settings')
        );

        // Add debug page in development
        if (defined('WP_DEBUG') && WP_DEBUG) {
            add_submenu_page(
                'modern-seo-tools',
                __('Debug', 'modern-seo-tools'),
                __('Debug', 'modern-seo-tools'),
                'manage_options',
                'modern-seo-tools-debug',
                array($this, 'admin_page_debug')
            );
        }
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'modern-seo-tools') === false) {
            return;
        }

        // Enqueue CodeMirror dependencies first
        wp_enqueue_code_editor(array('type' => 'text/html'));
        wp_enqueue_script('wp-theme-plugin-editor');
        wp_enqueue_style('wp-codemirror');

        // Enqueue underscore.js which is needed for CodeMirror
        wp_enqueue_script('underscore');

        wp_enqueue_style(
            'modern-seo-tools-admin',
            MODERN_SEO_TOOLS_PLUGIN_URL . 'admin/css/admin.css',
            array('wp-codemirror'),
            MODERN_SEO_TOOLS_VERSION
        );

        wp_enqueue_script(
            'modern-seo-tools-admin',
            MODERN_SEO_TOOLS_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery', 'wp-util', 'wp-codemirror', 'code-editor', 'underscore'),
            MODERN_SEO_TOOLS_VERSION,
            true
        );

        // Localize script
        wp_localize_script('modern-seo-tools-admin', 'mstAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('mst_admin_nonce'),
            'strings' => array(
                'confirmDelete' => __('Are you sure you want to delete this tool?', 'modern-seo-tools'),
                'saving' => __('Saving...', 'modern-seo-tools'),
                'saved' => __('Saved!', 'modern-seo-tools'),
                'error' => __('Error occurred. Please try again.', 'modern-seo-tools'),
            )
        ));
    }

    /**
     * Handle form submissions
     */
    public function handle_form_submissions() {
        if (!current_user_can('manage_options')) {
            return;
        }

        // Handle settings form
        if (isset($_POST['mst_save_settings']) && wp_verify_nonce($_POST['mst_nonce'], 'mst_settings')) {
            $this->save_settings();
        }
    }

    /**
     * Tools list admin page
     */
    public function admin_page_tools_list() {
        $tools = $this->database->get_tools();
        $total_tools = $this->database->get_tools_count();
        $active_tools = $this->database->get_tools_count('active');
        $inactive_tools = $this->database->get_tools_count('inactive');

        include MODERN_SEO_TOOLS_PLUGIN_DIR . 'admin/views/tools-list.php';
    }

    /**
     * Add/Edit tool admin page
     */
    public function admin_page_add_tool() {
        $tool_id = isset($_GET['edit']) ? intval($_GET['edit']) : 0;
        $tool = null;
        $is_edit = false;

        if ($tool_id > 0) {
            $tool = $this->database->get_tool($tool_id);
            $is_edit = true;

            if (!$tool) {
                wp_die(__('Tool not found.', 'modern-seo-tools'));
            }
        }

        include MODERN_SEO_TOOLS_PLUGIN_DIR . 'admin/views/tool-editor.php';
    }

    /**
     * Settings admin page
     */
    public function admin_page_settings() {
        $options = modern_seo_tools()->get_options();
        include MODERN_SEO_TOOLS_PLUGIN_DIR . 'admin/views/settings.php';
    }

    /**
     * Debug admin page
     */
    public function admin_page_debug() {
        echo '<div class="wrap">';
        echo '<h1>Modern SEO Tools - Debug Information</h1>';

        // Check table existence
        echo '<h2>Database Status</h2>';
        echo '<p><strong>Table exists:</strong> ' . ($this->database->table_exists() ? 'Yes' : 'No') . '</p>';

        // Check AJAX endpoints
        echo '<h2>AJAX Endpoints</h2>';
        echo '<p><strong>AJAX URL:</strong> ' . admin_url('admin-ajax.php') . '</p>';
        echo '<p><strong>Nonce:</strong> ' . wp_create_nonce('mst_admin_nonce') . '</p>';

        // Test database connection
        global $wpdb;
        echo '<h2>Database Connection</h2>';
        echo '<p><strong>WordPress DB Version:</strong> ' . $wpdb->db_version() . '</p>';
        echo '<p><strong>Table Name:</strong> ' . $wpdb->prefix . 'modern_seo_tools</p>';

        // Show any recent errors
        if ($wpdb->last_error) {
            echo '<h2>Recent Database Error</h2>';
            echo '<p style="color: red;"><strong>Error:</strong> ' . esc_html($wpdb->last_error) . '</p>';
        }

        // Test tool creation
        echo '<h2>Test Tool Creation</h2>';
        echo '<button type="button" id="test-tool-creation" class="button button-primary">Test Create Tool</button>';
        echo '<div id="test-results"></div>';

        echo '<script>
        jQuery(document).ready(function($) {
            $("#test-tool-creation").click(function() {
                var testData = {
                    action: "mst_save_tool",
                    nonce: "' . wp_create_nonce('mst_admin_nonce') . '",
                    tool_id: 0,
                    name: "Test Tool " + Date.now(),
                    shortcode: "test_tool_" + Date.now(),
                    description: "Test description",
                    html_code: "<p>Test HTML</p>",
                    css_code: "p { color: red; }",
                    js_code: "console.log(\"test\");",
                    status: "active"
                };

                $.post(ajaxurl, testData)
                    .done(function(response) {
                        $("#test-results").html("<h3>Response:</h3><pre>" + JSON.stringify(response, null, 2) + "</pre>");
                    })
                    .fail(function(xhr, status, error) {
                        $("#test-results").html("<h3>Error:</h3><p style=\"color: red;\">" + error + "</p>");
                    });
            });
        });
        </script>';

        echo '</div>';
    }

    /**
     * Save settings
     */
    private function save_settings() {
        $options = array(
            'enable_css_minification' => isset($_POST['enable_css_minification']),
            'enable_js_minification' => isset($_POST['enable_js_minification']),
            'load_css_in_head' => isset($_POST['load_css_in_head']),
            'load_js_in_footer' => isset($_POST['load_js_in_footer']),
            'enable_caching' => isset($_POST['enable_caching']),
            'cache_duration' => intval($_POST['cache_duration']),
        );

        modern_seo_tools()->update_options($options);

        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' .
                 __('Settings saved successfully!', 'modern-seo-tools') . '</p></div>';
        });
    }

    /**
     * AJAX: Save tool
     */
    public function ajax_save_tool() {
        // Debug: Log that AJAX handler was called
        error_log('MST: AJAX save tool handler called');
        error_log('MST: POST data: ' . print_r($_POST, true));

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'mst_admin_nonce')) {
            error_log('MST: Nonce verification failed');
            wp_send_json_error(__('Security check failed.', 'modern-seo-tools'));
        }

        if (!current_user_can('manage_options')) {
            error_log('MST: User lacks permissions');
            wp_send_json_error(__('Insufficient permissions.', 'modern-seo-tools'));
        }

        // Validate required fields
        if (empty($_POST['name']) || empty($_POST['shortcode'])) {
            error_log('MST: Missing required fields');
            wp_send_json_error(__('Name and shortcode are required.', 'modern-seo-tools'));
        }

        $tool_id = intval($_POST['tool_id']);
        $data = array(
            'name' => sanitize_text_field($_POST['name']),
            'shortcode' => sanitize_text_field($_POST['shortcode']),
            'description' => sanitize_textarea_field($_POST['description']),
            'html_code' => wp_unslash($_POST['html_code']),
            'css_code' => wp_unslash($_POST['css_code']),
            'js_code' => wp_unslash($_POST['js_code']),
            'status' => sanitize_text_field($_POST['status'])
        );

        error_log('MST: Prepared data: ' . print_r($data, true));

        if ($tool_id > 0) {
            // Update existing tool
            error_log('MST: Updating tool ID: ' . $tool_id);
            $result = $this->database->update_tool($tool_id, $data);
        } else {
            // Create new tool
            error_log('MST: Creating new tool');
            $result = $this->database->insert_tool($data);
        }

        error_log('MST: Database operation result: ' . print_r($result, true));

        if (is_wp_error($result)) {
            error_log('MST: Database error: ' . $result->get_error_message());
            wp_send_json_error($result->get_error_message());
        } else {
            $tool_id = $tool_id > 0 ? $tool_id : $result;
            $tool = $this->database->get_tool($tool_id);
            error_log('MST: Tool saved successfully, ID: ' . $tool_id);
            wp_send_json_success(array(
                'message' => __('Tool saved successfully!', 'modern-seo-tools'),
                'tool' => $tool,
                'redirect' => admin_url('admin.php?page=modern-seo-tools')
            ));
        }
    }

    /**
     * AJAX: Delete tool
     */
    public function ajax_delete_tool() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'mst_admin_nonce')) {
            wp_send_json_error(__('Security check failed.', 'modern-seo-tools'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'modern-seo-tools'));
        }

        $tool_id = intval($_POST['tool_id']);
        $result = $this->database->delete_tool($tool_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(__('Tool deleted successfully!', 'modern-seo-tools'));
        }
    }

    /**
     * AJAX: Toggle tool status
     */
    public function ajax_toggle_tool_status() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'mst_admin_nonce')) {
            wp_send_json_error(__('Security check failed.', 'modern-seo-tools'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions.', 'modern-seo-tools'));
        }

        $tool_id = intval($_POST['tool_id']);
        $tool = $this->database->get_tool($tool_id);

        if (!$tool) {
            wp_send_json_error(__('Tool not found.', 'modern-seo-tools'));
        }

        $new_status = $tool->status === 'active' ? 'inactive' : 'active';
        $result = $this->database->update_tool($tool_id, array('status' => $new_status));

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'status' => $new_status,
                'message' => sprintf(
                    __('Tool %s successfully!', 'modern-seo-tools'),
                    $new_status === 'active' ? __('activated', 'modern-seo-tools') : __('deactivated', 'modern-seo-tools')
                )
            ));
        }
    }
}
