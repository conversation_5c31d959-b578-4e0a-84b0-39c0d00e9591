/**
 * Admin JavaScript for Modern SEO Tools
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('Modern SEO Tools admin.js loaded');
        console.log('Available global objects:', {
            wp: typeof window.wp,
            codeEditor: typeof window.wp?.codeEditor,
            underscore: typeof window._
        });

        initToolsList();
        initToolEditor();
        initSettings();
        initModals();
    });

    /**
     * Initialize tools list functionality
     */
    function initToolsList() {
        // Copy shortcode functionality
        $('.mst-copy-shortcode').on('click', function(e) {
            e.preventDefault();
            const shortcode = $(this).data('shortcode');
            copyToClipboard(shortcode);
            showNotice('Shortcode copied to clipboard!', 'success');
        });

        // Toggle tool status
        $('.mst-toggle-status').on('click', function(e) {
            e.preventDefault();
            const toolId = $(this).data('tool-id');
            const button = $(this);

            toggleToolStatus(toolId, button);
        });

        // Delete tool
        $('.mst-delete-tool').on('click', function(e) {
            e.preventDefault();
            const toolId = $(this).data('tool-id');

            if (confirm(mstAdmin.strings.confirmDelete)) {
                deleteTool(toolId);
            }
        });

        // Duplicate tool
        $('.mst-duplicate-tool').on('click', function(e) {
            e.preventDefault();
            const toolId = $(this).data('tool-id');
            duplicateTool(toolId);
        });
    }

    /**
     * Initialize tool editor functionality
     */
    function initToolEditor() {
        // Tab switching
        $('.mst-tab-button').on('click', function(e) {
            e.preventDefault();
            const tab = $(this).data('tab');
            switchTab(tab);
        });

        // Generate shortcode
        $('#generate-shortcode').on('click', function(e) {
            e.preventDefault();
            generateShortcode();
        });

        // Shortcode preview
        $('#tool-shortcode').on('input', function() {
            updateShortcodePreview();
        });

        // Tool name to shortcode
        $('#tool-name').on('input', function() {
            if ($('#tool-shortcode').val() === '') {
                generateShortcodeFromName();
            }
        });

        // Code editor initialization - wait for dependencies to load
        function waitForCodeEditor() {
            if (window.wp && window.wp.codeEditor && typeof wp.codeEditor.initialize === 'function') {
                initCodeEditors();
            } else {
                setTimeout(waitForCodeEditor, 100);
            }
        }

        // Start checking after a short delay
        setTimeout(waitForCodeEditor, 200);

        // Preview functionality
        $('#refresh-preview').on('click', function(e) {
            e.preventDefault();
            refreshPreview();
        });

        // Save tool
        $('#mst-tool-form').on('submit', function(e) {
            e.preventDefault();
            saveTool();
        });

        // Save as new
        $('#save-as-new').on('click', function(e) {
            e.preventDefault();
            saveAsNew();
        });

        // Copy shortcode examples
        $('.mst-copy-text').on('click', function() {
            const text = $(this).data('copy');
            copyToClipboard(text);
            showNotice('Shortcode copied to clipboard!', 'success');
        });
    }

    /**
     * Initialize settings functionality
     */
    function initSettings() {
        // Clear cache
        $('#clear-all-cache').on('click', function(e) {
            e.preventDefault();
            clearCache();
        });

        // Export/Import
        $('#export-all-tools').on('click', function(e) {
            e.preventDefault();
            exportTools();
        });

        $('#export-settings').on('click', function(e) {
            e.preventDefault();
            exportSettings();
        });

        $('#import-tools').on('click', function(e) {
            e.preventDefault();
            $('#import-file').click();
        });

        $('#import-file').on('change', function() {
            importFile(this.files[0]);
        });

        // Reset settings
        $('#reset-settings').on('click', function(e) {
            e.preventDefault();
            if (confirm('Are you sure you want to reset all settings to defaults?')) {
                resetSettings();
            }
        });
    }

    /**
     * Initialize modal functionality
     */
    function initModals() {
        // Close modal
        $('.mst-modal-close').on('click', function() {
            $(this).closest('.mst-modal').hide();
        });

        // Close modal on background click
        $('.mst-modal').on('click', function(e) {
            if (e.target === this) {
                $(this).hide();
            }
        });
    }

    /**
     * Switch tabs in tool editor
     */
    function switchTab(tab) {
        $('.mst-tab-button').removeClass('active');
        $('.mst-tab-pane').removeClass('active');

        $('[data-tab="' + tab + '"]').addClass('active');
        $('#tab-' + tab).addClass('active');

        // Refresh CodeMirror if available
        try {
            if (window.wp && window.wp.codeEditor && window.wp.codeEditor.instances) {
                const editor = window.wp.codeEditor.instances['#' + tab + '-code'];
                if (editor && editor.codemirror && typeof editor.codemirror.refresh === 'function') {
                    setTimeout(() => editor.codemirror.refresh(), 100);
                }
            }
        } catch (error) {
            console.warn('Failed to refresh CodeMirror for tab:', tab, error);
        }
    }

    /**
     * Initialize code editors
     */
    function initCodeEditors() {
        // Check if we're on the tool editor page
        if (!$('#html-code').length) {
            return;
        }

        console.log('Initializing code editors...');

        // Check if CodeMirror is available
        if (!window.wp || !window.wp.codeEditor || typeof wp.codeEditor.initialize !== 'function') {
            console.warn('CodeMirror not available, using regular textareas');
            return;
        }

        try {
            // Initialize instances object if it doesn't exist
            if (!window.wp.codeEditor.instances) {
                window.wp.codeEditor.instances = {};
            }

            // Ensure underscore.js is available or create fallback
            var _ = window._ || {
                clone: function(obj) {
                    return obj ? Object.assign({}, obj) : {};
                },
                extend: function(dest, src) {
                    return Object.assign(dest || {}, src || {});
                }
            };

            // Get default settings or create basic ones
            var defaultSettings = wp.codeEditor.defaultSettings || {
                codemirror: {
                    lineNumbers: true,
                    lineWrapping: true,
                    theme: 'default'
                }
            };

            // HTML Editor
            const htmlSettings = _.clone(defaultSettings);
            htmlSettings.codemirror = _.extend({}, htmlSettings.codemirror || {}, {
                mode: 'htmlmixed',
                lineNumbers: true,
                lineWrapping: true,
                theme: 'default'
            });

            const htmlEditor = wp.codeEditor.initialize($('#html-code'), htmlSettings);
            if (htmlEditor && htmlEditor.codemirror) {
                console.log('HTML editor initialized');
                window.wp.codeEditor.instances['#html-code'] = htmlEditor;
                htmlEditor.codemirror.on('change', function() {
                    refreshPreview();
                });
            }

            // CSS Editor
            const cssSettings = _.clone(defaultSettings);
            cssSettings.codemirror = _.extend({}, cssSettings.codemirror || {}, {
                mode: 'css',
                lineNumbers: true,
                lineWrapping: true,
                theme: 'default'
            });

            const cssEditor = wp.codeEditor.initialize($('#css-code'), cssSettings);
            if (cssEditor && cssEditor.codemirror) {
                console.log('CSS editor initialized');
                window.wp.codeEditor.instances['#css-code'] = cssEditor;
                cssEditor.codemirror.on('change', function() {
                    refreshPreview();
                });
            }

            // JavaScript Editor
            const jsSettings = _.clone(defaultSettings);
            jsSettings.codemirror = _.extend({}, jsSettings.codemirror || {}, {
                mode: 'javascript',
                lineNumbers: true,
                lineWrapping: true,
                theme: 'default'
            });

            const jsEditor = wp.codeEditor.initialize($('#js-code'), jsSettings);
            if (jsEditor && jsEditor.codemirror) {
                console.log('JavaScript editor initialized');
                window.wp.codeEditor.instances['#js-code'] = jsEditor;
                jsEditor.codemirror.on('change', function() {
                    refreshPreview();
                });
            }

            console.log('Code editors initialization completed');
            console.log('Available instances:', Object.keys(window.wp.codeEditor.instances));

        } catch (error) {
            console.error('CodeMirror initialization failed:', error);
            console.warn('Falling back to regular textareas');
        }
    }

    /**
     * Generate shortcode from tool name
     */
    function generateShortcodeFromName() {
        const name = $('#tool-name').val();
        if (name) {
            const shortcode = name.toLowerCase()
                .replace(/[^a-z0-9\s-_]/g, '')
                .replace(/\s+/g, '_')
                .replace(/_+/g, '_')
                .replace(/^_|_$/g, '');

            $('#tool-shortcode').val(shortcode);
            updateShortcodePreview();
        }
    }

    /**
     * Generate shortcode
     */
    function generateShortcode() {
        generateShortcodeFromName();
    }

    /**
     * Update shortcode preview
     */
    function updateShortcodePreview() {
        const shortcode = $('#tool-shortcode').val();
        if (shortcode) {
            $('#shortcode-preview-text').text('[' + shortcode + ']');
            $('#shortcode-preview').show();
        } else {
            $('#shortcode-preview').hide();
        }
    }

    /**
     * Refresh preview
     */
    function refreshPreview() {
        const htmlCode = getEditorValue('#html-code');
        const cssCode = getEditorValue('#css-code');
        const jsCode = getEditorValue('#js-code');

        let previewHtml = '<div class="mst-preview-content">';

        if (cssCode) {
            previewHtml += '<style>' + cssCode + '</style>';
        }

        if (htmlCode) {
            previewHtml += htmlCode;
        } else {
            previewHtml += '<div class="mst-preview-placeholder"><span class="dashicons dashicons-visibility"></span><p>Add HTML code to see preview</p></div>';
        }

        previewHtml += '</div>';

        if (jsCode) {
            previewHtml += '<script>jQuery(document).ready(function($) { ' + jsCode + ' });</script>';
        }

        $('#tool-preview').html(previewHtml);
    }

    /**
     * Get editor value (CodeMirror or textarea)
     */
    function getEditorValue(selector) {
        try {
            // First try to get from CodeMirror if available
            if (window.wp &&
                window.wp.codeEditor &&
                window.wp.codeEditor.instances &&
                typeof window.wp.codeEditor.instances === 'object') {

                const editor = window.wp.codeEditor.instances[selector];
                if (editor && editor.codemirror && typeof editor.codemirror.getValue === 'function') {
                    console.log('Getting value from CodeMirror for:', selector);
                    return editor.codemirror.getValue();
                }
            }
        } catch (error) {
            console.warn('CodeMirror access failed for ' + selector + ', falling back to textarea:', error);
        }

        // Fallback to regular textarea value
        const element = $(selector);
        const value = element.length ? element.val() : '';
        console.log('Getting value from textarea for:', selector, 'Length:', value.length);
        return value;
    }

    /**
     * Save tool
     */
    function saveTool() {
        // Validate required fields
        const name = $('#tool-name').val().trim();
        const shortcode = $('#tool-shortcode').val().trim();

        if (!name) {
            showNotice('Tool name is required.', 'error');
            $('#tool-name').focus();
            return;
        }

        if (!shortcode) {
            showNotice('Shortcode is required.', 'error');
            $('#tool-shortcode').focus();
            return;
        }

        // Debug: Check editor values
        console.log('Getting editor values...');
        console.log('HTML element exists:', $('#html-code').length > 0);
        console.log('CSS element exists:', $('#css-code').length > 0);
        console.log('JS element exists:', $('#js-code').length > 0);

        const htmlCode = getEditorValue('#html-code');
        const cssCode = getEditorValue('#css-code');
        const jsCode = getEditorValue('#js-code');

        console.log('HTML code length:', htmlCode.length);
        console.log('CSS code length:', cssCode.length);
        console.log('JS code length:', jsCode.length);

        const formData = {
            action: 'mst_save_tool',
            nonce: mstAdmin.nonce,
            tool_id: $('#tool-id').val(),
            name: name,
            shortcode: shortcode,
            description: $('#tool-description').val(),
            html_code: htmlCode,
            css_code: cssCode,
            js_code: jsCode,
            status: $('#tool-status').val()
        };

        // Debug: Log form data
        console.log('Saving tool with data:', formData);

        showLoading();

        $.post(mstAdmin.ajaxUrl, formData)
            .done(function(response) {
                hideLoading();
                console.log('AJAX Response:', response);

                if (response.success) {
                    showNotice(response.data.message, 'success');
                    if (response.data.redirect) {
                        setTimeout(() => {
                            window.location.href = response.data.redirect;
                        }, 1000);
                    }
                } else {
                    showNotice(response.data, 'error');
                }
            })
            .fail(function(xhr, status, error) {
                hideLoading();
                console.error('AJAX Error:', xhr, status, error);
                showNotice('AJAX Error: ' + error, 'error');
            });
    }

    /**
     * Toggle tool status
     */
    function toggleToolStatus(toolId, button) {
        const formData = {
            action: 'mst_toggle_tool_status',
            nonce: mstAdmin.nonce,
            tool_id: toolId
        };

        button.prop('disabled', true);

        $.post(mstAdmin.ajaxUrl, formData)
            .done(function(response) {
                if (response.success) {
                    showNotice(response.data.message, 'success');
                    location.reload();
                } else {
                    showNotice(response.data, 'error');
                }
            })
            .fail(function() {
                showNotice(mstAdmin.strings.error, 'error');
            })
            .always(function() {
                button.prop('disabled', false);
            });
    }

    /**
     * Delete tool
     */
    function deleteTool(toolId) {
        const formData = {
            action: 'mst_delete_tool',
            nonce: mstAdmin.nonce,
            tool_id: toolId
        };

        $.post(mstAdmin.ajaxUrl, formData)
            .done(function(response) {
                if (response.success) {
                    showNotice(response.data, 'success');
                    $('[data-tool-id="' + toolId + '"]').fadeOut();
                } else {
                    showNotice(response.data, 'error');
                }
            })
            .fail(function() {
                showNotice(mstAdmin.strings.error, 'error');
            });
    }

    /**
     * Copy text to clipboard
     */
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text);
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    }

    /**
     * Show notice
     */
    function showNotice(message, type) {
        const notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
        $('.wrap h1').after(notice);

        setTimeout(() => {
            notice.fadeOut();
        }, 5000);
    }

    /**
     * Show loading overlay
     */
    function showLoading() {
        $('#mst-loading-overlay').show();
    }

    /**
     * Hide loading overlay
     */
    function hideLoading() {
        $('#mst-loading-overlay').hide();
    }

    /**
     * Clear cache
     */
    function clearCache() {
        // Implementation for clearing cache
        showNotice('Cache cleared successfully!', 'success');
    }

    /**
     * Export tools
     */
    function exportTools() {
        // Implementation for exporting tools
        showNotice('Export functionality coming soon!', 'info');
    }

    /**
     * Export settings
     */
    function exportSettings() {
        // Implementation for exporting settings
        showNotice('Export functionality coming soon!', 'info');
    }

})(jQuery);
